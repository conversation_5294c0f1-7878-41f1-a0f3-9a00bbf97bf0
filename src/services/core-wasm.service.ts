import * as core_wasm from '@core/pkg/pianorhythm_core';
import { AudioSynthActions, AppStateActions, AppStateActions_Action } from '~/proto/pianorhythm-actions';
import { CoreService, ICoreWasmService } from '~/types/app.types';
import { WasmSynth } from '~/types/audio.types';

export default class CoreWasmService implements ICoreWasmService {
  static Initialized: boolean = false;

  getWasmInstance(): any {
    return core_wasm;
  }

  async initWithPayload(payload: any): Promise<void> {
    if (CoreWasmService.Initialized) return;
    if (!payload) throw new Error("Payload is required to initialize WASM module.");

    await core_wasm.default(payload.module, payload.memory);
    core_wasm.init_wasm();
    CoreWasmService.Initialized = true;
  }

  async createCoreWasmWebNonWebWorker(
    onHandleAppEvent: (event: number) => void,
    onHandleAppEffects: (bytes: Uint8Array) => void,
    handle_core_app_actions: (action: any) => void,
  ): Promise<CoreService> {
    if (CoreWasmService.Initialized) throw new Error("Core WASM already initialized.");

    await core_wasm.default();

    core_wasm.init_wasm();
    core_wasm.init_note_buffer_engine();

    // Start adaptive flushing instead of fixed interval
    core_wasm.start_adaptive_note_buffer_flushing();

    window.addEventListener("app_events", (event) => {
      let data = ((event as any).detail as Array<number>);
      data.forEach(onHandleAppEvent);
    });

    window.addEventListener("app_effects", (event) => {
      let bytes = ((event as any).detail as Uint8Array);
      onHandleAppEffects(bytes);
    });

    let coreService = {
      ...CoreService.DEFAULT,
      ...WasmSynth.DEFAULT,
      ...core_wasm as any,
      create_synth: (synth_engine, sample_rate) => {
        let desc = new core_wasm.PianoRhythmSynthesizerDescriptor(synth_engine, sample_rate);
        core_wasm.create_synth(desc);
      },
      get_hash_socket_id: (socket_id) => {
        return Promise.resolve(core_wasm.get_hash_socket_id(socket_id));
      },
      send_app_action: handle_core_app_actions,
      send_app_synth_action: (action: AudioSynthActions) => {
        handle_core_app_actions(AppStateActions.create({
          action: AppStateActions_Action.SynthAction,
          audioSynthAction: AudioSynthActions.create(action)
        }));
      },
      send_app_action_bytes: function (action: Uint8Array) {
        handle_core_app_actions(AppStateActions.decode(action));
      },
    } as CoreService;

    return coreService;
  };
}